# Flutter/Dart
.dart_tool/
.packages
.pub-cache/
.pub/
build/
.flutter-plugins
.flutter-plugins-dependencies
.metadata

# Android
android/app/debug
android/app/profile
android/app/release
android/.gradle/
android/captures/
android/gradlew
android/gradlew.bat
android/local.properties
android/**/GeneratedPluginRegistrant.java

# iOS
ios/Flutter/Flutter.framework
ios/Flutter/Flutter.podspec
ios/Flutter/Generated.xcconfig
ios/Flutter/app.flx
ios/Flutter/app.zip
ios/Flutter/flutter_assets/
ios/Flutter/flutter_export_environment.sh
ios/Pods/
ios/Runner.xcworkspace/
ios/.symlinks/
ios/Flutter/ephemeral/

# Python Backend
backend/venv/
backend/__pycache__/
backend/**/__pycache__/
backend/**/**/__pycache__/
*.pyc
*.pyo
*.pyd
__pycache__/
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Database
*.db
*.sqlite
*.sqlite3

# Environment variables
.env
.env.local
.env.production
.env.test

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Testing
coverage/
.nyc_output/
.coverage
htmlcov/
.pytest_cache/

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Node modules (if any)
node_modules/

# Build artifacts
dist/
build/
out/

# API Keys and secrets
*.key
*.pem
secrets/
